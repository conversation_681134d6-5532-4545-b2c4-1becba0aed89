/**
 * Upgrade command implementation
 * Upgrades an agent project to match the latest template
 */

import { existsSync } from "fs";
import { join } from "path";

import chalk from "chalk";
import inquirer from "inquirer";
import ora from "ora";

import type {
  ProjectAnalysisResult,
  TemplateComparisonResult,
  UpgradeCommandOptions,
  UpgradeReport,
  ValidationResult,
} from "../types.js";
import { DependencyUpdater } from "../utils/dependency-updater.js";
import { FileModifier } from "../utils/file-modifier.js";
import { Logger } from "../utils/logger.js";
import { ProjectUpgradeAnalyzer } from "../utils/project-upgrade-analyzer.js";
import { TemplateComparator } from "../utils/template-comparator.js";

export class UpgradeCommand {
  private projectAnalyzer: ProjectUpgradeAnalyzer;
  private templateComparator: TemplateComparator;
  private dependencyUpdater: DependencyUpdater;
  private fileModifier: FileModifier;

  constructor() {
    this.projectAnalyzer = new ProjectUpgradeAnalyzer();
    this.templateComparator = new TemplateComparator();
    this.dependencyUpdater = new DependencyUpdater();
    this.fileModifier = new FileModifier();
  }

  /**
   * Validate target path is a valid agent project
   */
  private validateTargetPath(targetPath: string): ValidationResult {
    if (!existsSync(targetPath)) {
      return {
        valid: false,
        error: `Target path does not exist: ${targetPath}`,
      };
    }

    const packageJsonPath = join(targetPath, "package.json");
    if (!existsSync(packageJsonPath)) {
      return {
        valid: false,
        error: "No package.json found. This doesn't appear to be a Node.js project.",
      };
    }

    const srcPath = join(targetPath, "src");
    if (!existsSync(srcPath)) {
      return {
        valid: false,
        error: "No src directory found. This doesn't appear to be an agent project.",
      };
    }

    return { valid: true };
  }

  /**
   * Interactive mode for upgrade
   */
  private async runInteractive(): Promise<void> {
    Logger.title("CSCS Agent Project Upgrader");

    // Get target path
    const { targetPath } = await inquirer.prompt([
      {
        type: "input",
        name: "targetPath",
        message: "Target project path:",
        default: process.cwd(),
        validate: (input: string) => {
          const validation = this.validateTargetPath(input);
          return validation.valid || validation.error || "Invalid target path";
        },
      },
    ]);

    // Get upgrade options
    const options = await inquirer.prompt([
      {
        type: "confirm",
        name: "dryRun",
        message: "Run in dry-run mode (preview changes without applying)?",
        default: false,
      },
      {
        type: "confirm",
        name: "skipBackup",
        message: "Skip creating backup files?",
        default: false,
      },
      {
        type: "confirm",
        name: "force",
        message: "Force upgrade even if there are warnings?",
        default: false,
      },
    ]);

    // Confirm upgrade
    const { confirm } = await inquirer.prompt([
      {
        type: "confirm",
        name: "confirm",
        message: `Upgrade project at '${chalk.cyan(targetPath)}'?`,
        default: true,
      },
    ]);

    if (!confirm) {
      Logger.info("Upgrade cancelled");
      return;
    }

    // Execute upgrade
    const success = await this.executeUpgrade({
      targetPath,
      interactive: true,
      ...options,
    });

    if (!success) {
      process.exit(1);
    }
  }

  /**
   * Non-interactive mode for upgrade
   */
  private async runNonInteractive(options: UpgradeCommandOptions): Promise<void> {
    const targetPath = options.targetPath || process.cwd();

    // Validate target path
    const validation = this.validateTargetPath(targetPath);
    if (!validation.valid) {
      Logger.error(validation.error || "Invalid target path");
      process.exit(1);
    }

    // Execute upgrade
    const success = await this.executeUpgrade({
      ...options,
      targetPath,
    });

    if (!success) {
      process.exit(1);
    }
  }

  /**
   * Execute the upgrade process
   */
  private async executeUpgrade(options: UpgradeCommandOptions): Promise<boolean> {
    const targetPath = options.targetPath!;

    try {
      Logger.info(`Starting upgrade for project: ${chalk.cyan(targetPath)}`);

      // Phase 1: File Analysis
      const spinner = ora("Analyzing project files...").start();
      const projectAnalysis = await this.projectAnalyzer.analyzeProject(targetPath);

      if (!projectAnalysis.isValidAgentProject) {
        spinner.fail("Project analysis failed");
        Logger.error("This doesn't appear to be a valid CSCS Agent project");
        return false;
      }

      spinner.succeed("Project analysis completed");

      // Phase 2: Template Comparison
      spinner.start("Comparing with latest template...");
      const templateComparisons = await this.templateComparator.compareWithTemplate(projectAnalysis);
      spinner.succeed("Template comparison completed");

      // Phase 3: Generate upgrade report
      const upgradeReport = await this.generateUpgradeReport(projectAnalysis, templateComparisons);

      // Show upgrade report
      this.displayUpgradeReport(upgradeReport);

      // Check if dry run
      if (options.dryRun) {
        Logger.info("Dry run completed. No changes were made.");
        return true;
      }

      // Confirm before applying changes
      if (options.interactive && !options.force) {
        const { proceed } = await inquirer.prompt([
          {
            type: "confirm",
            name: "proceed",
            message: "Apply these changes?",
            default: true,
          },
        ]);

        if (!proceed) {
          Logger.info("Upgrade cancelled");
          return true;
        }
      }

      // Phase 4: Apply dependency updates
      if (upgradeReport.dependencyUpdates.length > 0) {
        spinner.start("Updating dependencies...");
        await this.dependencyUpdater.updateDependencies(targetPath, upgradeReport.dependencyUpdates);
        spinner.succeed("Dependencies updated");
      }

      // Phase 5: Apply file modifications
      if (upgradeReport.fileModifications.length > 0) {
        spinner.start("Modifying files...");
        await this.fileModifier.applyModifications(targetPath, upgradeReport.fileModifications, !options.skipBackup);
        spinner.succeed("Files modified");
      }

      Logger.success("Upgrade completed successfully!");
      this.showSuccessMessage(upgradeReport);

      return true;
    } catch (error) {
      Logger.error(`Upgrade failed: ${error instanceof Error ? error.message : "Unknown error"}`);
      return false;
    }
  }

  /**
   * Display upgrade report
   */
  private displayUpgradeReport(report: UpgradeReport): void {
    Logger.info("\n" + chalk.bold("Upgrade Report:"));

    // Show summary
    const { summary } = report;
    Logger.info(`  Total files to check: ${summary.totalFiles}`);
    Logger.info(`  Files to modify: ${summary.modifiedFiles}`);
    Logger.info(`  Dependencies to add: ${summary.addedDependencies}`);
    Logger.info(`  Dependencies to update: ${summary.updatedDependencies}`);

    if (summary.criticalChanges > 0) {
      Logger.warning(`  Critical changes: ${summary.criticalChanges}`);
    }

    // Show warnings
    if (summary.warnings.length > 0) {
      Logger.warning("\nWarnings:");
      summary.warnings.forEach((warning) => Logger.warning(`  - ${warning}`));
    }

    // Show errors
    if (summary.errors.length > 0) {
      Logger.error("\nErrors:");
      summary.errors.forEach((error) => Logger.error(`  - ${error}`));
    }
  }

  /**
   * Show success message
   */
  private showSuccessMessage(report: UpgradeReport): void {
    Logger.info("\n" + chalk.green("✨ Upgrade completed successfully!"));
    Logger.info("\nNext steps:");
    Logger.info("  1. Review the changes made to your project");
    Logger.info("  2. Test your application to ensure everything works");
    Logger.info("  3. Commit your changes to version control");

    if (report.summary.warnings.length > 0) {
      Logger.info("  4. Address any warnings mentioned above");
    }
  }

  /**
   * Generate comprehensive upgrade report
   */
  private async generateUpgradeReport(
    projectAnalysis: ProjectAnalysisResult,
    templateComparisons: TemplateComparisonResult[],
  ): Promise<UpgradeReport> {
    const dependencyUpdates: any[] = [];
    const fileModifications: any[] = [];
    let criticalChanges = 0;
    const warnings: string[] = [];
    const errors: string[] = [];

    // Process template comparisons to generate updates
    for (const comparison of templateComparisons) {
      if (comparison.file === "package.json" && comparison.requiresUpdate) {
        // Generate dependency updates
        try {
          const projectPackage = JSON.parse(projectAnalysis.packageJson.content);
          const templatePackage = JSON.parse(comparison.templateContent);

          const updates = this.dependencyUpdater.generateDependencyUpdates(
            projectPackage.dependencies || {},
            projectPackage.devDependencies || {},
            templatePackage.dependencies || {},
            templatePackage.devDependencies || {},
          );

          dependencyUpdates.push(...updates);
        } catch (error) {
          errors.push(
            `Failed to process package.json updates: ${error instanceof Error ? error.message : "Unknown error"}`,
          );
        }
      }

      if (comparison.requiresUpdate) {
        // Generate file modifications
        const modifications = this.fileModifier.generateFileModifications(comparison.file, comparison.differences);
        fileModifications.push(...modifications);

        // Count critical changes
        if (comparison.updatePriority === "critical") {
          criticalChanges++;
        }

        // Add warnings for recommended changes
        if (comparison.updatePriority === "recommended") {
          warnings.push(`Recommended update for ${comparison.file}`);
        }
      }
    }

    // Validate dependency updates
    if (dependencyUpdates.length > 0) {
      const validation = this.dependencyUpdater.validateDependencyVersions(dependencyUpdates);
      if (!validation.valid) {
        errors.push(...validation.errors);
      }
    }

    // Validate file modifications
    if (fileModifications.length > 0) {
      const validation = this.fileModifier.validateModifications(fileModifications);
      if (!validation.valid) {
        errors.push(...validation.errors);
      }
    }

    return {
      projectAnalysis,
      templateComparisons,
      dependencyUpdates,
      fileModifications,
      summary: {
        totalFiles: templateComparisons.length,
        modifiedFiles: fileModifications.length,
        addedDependencies: dependencyUpdates.filter((u) => u.currentVersion === "not installed").length,
        updatedDependencies: dependencyUpdates.filter((u) => u.currentVersion !== "not installed").length,
        removedDependencies: 0, // Not implemented yet
        criticalChanges,
        warnings,
        errors,
      },
    };
  }

  /**
   * Execute upgrade command
   */
  async execute(options: UpgradeCommandOptions = {}): Promise<void> {
    try {
      if (options.interactive) {
        await this.runInteractive();
      } else {
        await this.runNonInteractive(options);
      }
    } catch (error) {
      Logger.error(`Failed to upgrade project: ${error instanceof Error ? error.message : "Unknown error"}`);
      process.exit(1);
    }
  }
}
