/**
 * Project upgrade analyzer using LLM integration
 * Analyzes project files to understand current state and configuration
 */

import { existsSync } from "fs";
import { join } from "path";

import { Agent, run } from "@openai/agents";

import type { FileAnalysisResult, FileConfig, PackageJson, ProjectAnalysisResult } from "../types.js";
import { FileSystemManager } from "./filesystem-manager.js";
import { Logger } from "./logger.js";

// Define core file patterns for discovery
const coreFilePatterns: FileConfig[] = [
  {
    filePath: "package.json",
    analysisContext: "Package configuration and dependencies",
    required: true,
    priority: 10,
  },
  {
    filePath: "src/main.tsx",
    alternativePaths: ["src/main.ts", "src/index.tsx", "src/index.ts", "src/App.tsx", "src/App.ts"],
    analysisContext: "Main application entry point",
    required: true,
    priority: 9,
  },
  {
    filePath: "src/agent-config.tsx",
    alternativePaths: ["src/agent-config.ts", "src/config/agent.tsx", "src/config/agent.ts"],
    analysisContext: "Agent configuration and setup",
    required: false,
    priority: 8,
  },
];

export class ProjectUpgradeAnalyzer {
  private fsManager: FileSystemManager;
  private agent?: Agent;

  constructor() {
    this.fsManager = new FileSystemManager();

    // Initialize the LLM agent for project analysis only if API key is available
    if (process.env.OPENAI_API_KEY) {
      this.agent = new Agent({
        name: "ProjectAnalyzer",
        instructions: `You are an expert TypeScript/React developer specializing in CSCS Agent projects.
          Your task is to intelligently analyze project files and provide detailed insights about their structure, dependencies, and configuration.

          For each file you analyze, you should:
          1. AUTOMATICALLY IDENTIFY the file type and purpose based on its path, extension, and content
          2. Determine the file's role in the project (e.g., package configuration, entry point, component, config, etc.)
          3. Analyze the file's structure, dependencies, and patterns
          4. Identify any custom modifications or non-standard patterns
          5. Assess compatibility with the latest CSCS Agent framework
          6. Provide specific recommendations for upgrades or improvements

          File Type Intelligence Guidelines:
          - package.json: Focus on dependencies, scripts, and project metadata
          - main/index files: Analyze app initialization, routing, and core imports
          - config files: Examine configuration patterns and customizations
          - component files: Review React patterns, hooks usage, and CSCS Agent integrations
          - TypeScript files: Check type definitions, interfaces, and modern TS features
          - Build/tooling files: Assess build configuration and development setup

          Always provide structured, actionable analysis that can guide upgrade decisions.
          Be specific about version compatibility and migration requirements.`,
        model: process.env.OPENAI_MODEL || "gpt-4o",
      });
    }
  }

  /**
   * Analyze a complete project using dynamic file discovery
   */
  async analyzeProject(projectPath: string): Promise<ProjectAnalysisResult> {
    Logger.info(`Analyzing project at: ${projectPath}`);

    // Discover and analyze all relevant files
    const allFiles = await this.discoverAndAnalyzeFiles(projectPath);

    // Extract core files for backward compatibility
    const packageJson = this.findFileByPattern(allFiles, "package.json") || this.createEmptyResult(join(projectPath, "package.json"));
    const mainTsx = this.findMainEntryFile(allFiles, projectPath) || this.createEmptyResult(join(projectPath, "src/main.tsx"));
    const agentConfig = this.findAgentConfigFile(allFiles, projectPath) || this.createEmptyResult(join(projectPath, "src/agent-config.tsx"));

    // Separate additional files (non-core files)
    const additionalFiles = allFiles.filter(file =>
      !this.isCoreFile(file.filePath) && file.exists
    );

    // Create files index for easy lookup
    const filesIndex: Record<string, FileAnalysisResult> = {};
    allFiles.forEach(file => {
      const normalizedPath = this.normalizePath(file.filePath, projectPath);
      filesIndex[normalizedPath] = file;
    });

    // Generate project summary using LLM
    const projectSummary = await this.generateProjectSummary(allFiles, projectPath);

    // Determine if this is a valid agent project
    const isValidAgentProject = this.validateAgentProject(packageJson, mainTsx, agentConfig);

    return {
      files: filesIndex,
      packageJson,
      mainTsx,
      agentConfig,
      additionalFiles,
      projectPath,
      isValidAgentProject,
      projectSummary,
    };
  }

  /**
   * Discover and analyze all relevant files in the project
   */
  private async discoverAndAnalyzeFiles(projectPath: string): Promise<FileAnalysisResult[]> {
    Logger.info("Discovering and analyzing project files...");

    const allFiles: FileAnalysisResult[] = [];

    // Analyze core files first
    for (const pattern of coreFilePatterns) {
      const result = await this.analyzeFileByPattern(projectPath, pattern);
      if (result) {
        allFiles.push(result);
      }
    }

    // Discover and analyze additional configuration files
    const additionalFiles = await this.discoverAdditionalFiles(projectPath);
    allFiles.push(...additionalFiles);

    // Sort by priority (higher priority first)
    return allFiles.sort((a, b) => (b.priority || 0) - (a.priority || 0));
  }

  /**
   * Generic method to analyze multiple files in batch (kept for backward compatibility)
   */
  async analyzeFiles(projectPath: string, fileConfigs: FileConfig[]): Promise<Record<string, FileAnalysisResult>> {
    Logger.info(`Analyzing ${fileConfigs.length} files in batch...`);

    const results: Record<string, FileAnalysisResult> = {};

    for (const config of fileConfigs) {
      const result = await this.analyzeFileByPattern(projectPath, config);
      if (result) {
        // Use file path as key since we removed the type property
        const key = this.getFileKey(result.filePath);
        results[key] = result;
      }
    }

    return results;
  }

  /**
   * Analyze a single file based on pattern configuration
   */
  private async analyzeFileByPattern(projectPath: string, config: FileConfig): Promise<FileAnalysisResult | null> {
    // Build list of paths to check
    const pathsToCheck = [
      join(projectPath, config.filePath),
      ...(config.alternativePaths || []).map((path) => join(projectPath, path)),
    ];

    // Find the first existing file
    for (const filePath of pathsToCheck) {
      if (existsSync(filePath)) {
        const content = await this.fsManager.readFile(filePath);

        // Use enhanced LLM analysis with intelligent file type detection
        const analysisResult = await this.analyzeFileWithEnhancedLLM(filePath, content, config.analysisContext);

        return {
          filePath,
          content,
          exists: true,
          analysis: analysisResult.analysis,
          dependencies: analysisResult.dependencies,
          devDependencies: analysisResult.devDependencies,
          priority: config.priority,
          category: analysisResult.category,
        };
      }
    }

    // Return null if no file found (required files will be handled separately)
    return null;
  }

  /**
   * Create an empty file analysis result
   */
  private createEmptyResult(filePath: string): FileAnalysisResult {
    return {
      filePath,
      content: "",
      exists: false,
      priority: 0,
    };
  }

  /**
   * Get a normalized key for file indexing
   */
  private getFileKey(filePath: string): string {
    const fileName = filePath.split(/[/\\]/).pop() || "unknown";
    return fileName.replace(/\.[^/.]+$/, ""); // Remove extension
  }

  /**
   * Enhanced LLM analysis with intelligent file type detection
   */
  private async analyzeFileWithEnhancedLLM(
    filePath: string,
    content: string,
    context: string,
  ): Promise<{
    analysis: string;
    dependencies?: Record<string, string>;
    devDependencies?: Record<string, string>;
    category?: string;
  }> {
    // Handle package.json specially to extract dependencies
    if (filePath.endsWith("package.json")) {
      try {
        const packageData: PackageJson = JSON.parse(content);
        const analysis = await this.analyzeFileWithLLM(filePath, content, context);

        return {
          analysis,
          dependencies: packageData.dependencies || {},
          devDependencies: packageData.devDependencies || {},
          category: "package-config",
        };
      } catch (error) {
        Logger.warning(`Failed to parse package.json: ${error instanceof Error ? error.message : "Unknown error"}`);
        return {
          analysis: "Failed to parse package.json - invalid JSON format",
          category: "package-config",
        };
      }
    }

    // For other files, use enhanced LLM analysis
    const analysis = await this.analyzeFileWithLLM(filePath, content, context);
    const category = this.determineFileCategory(filePath, content);

    return {
      analysis,
      category,
    };
  }

  /**
   * Determine file category based on path and content
   */
  private determineFileCategory(filePath: string, content: string): string {
    const fileName = filePath.split(/[/\\]/).pop()?.toLowerCase() || "";

    if (fileName === "package.json") return "package-config";
    if (fileName.includes("main.") || fileName.includes("index.") || fileName.includes("app.")) return "entry-point";
    if (fileName.includes("config") || fileName.includes("agent")) return "configuration";
    if (fileName.includes("vite.config") || fileName.includes("tsconfig") || fileName.includes("eslint")) return "build-config";
    if (fileName.includes("tailwind")) return "style-config";
    if (fileName.endsWith(".tsx") || fileName.endsWith(".jsx")) return "react-component";
    if (fileName.endsWith(".ts") || fileName.endsWith(".js")) return "typescript-module";
    if (fileName.endsWith(".json")) return "json-config";

    return "other";
  }

  /**
   * Find file by pattern in analyzed files
   */
  private findFileByPattern(files: FileAnalysisResult[], pattern: string): FileAnalysisResult | null {
    return files.find(file => file.filePath.endsWith(pattern) && file.exists) || null;
  }

  /**
   * Find main entry file from analyzed files
   */
  private findMainEntryFile(files: FileAnalysisResult[], projectPath: string): FileAnalysisResult | null {
    const mainPatterns = ["main.tsx", "main.ts", "index.tsx", "index.ts", "App.tsx", "App.ts"];

    for (const pattern of mainPatterns) {
      const file = files.find((f) => f.filePath.includes(pattern) && f.exists);
      if (file) return file;
    }

    return null;
  }

  /**
   * Find agent config file from analyzed files
   */
  private findAgentConfigFile(files: FileAnalysisResult[], projectPath: string): FileAnalysisResult | null {
    const configPatterns = ["agent-config.tsx", "agent-config.ts", "agent.tsx", "agent.ts"];

    for (const pattern of configPatterns) {
      const file = files.find((f) => f.filePath.includes(pattern) && f.exists);
      if (file) return file;
    }

    return null;
  }

  /**
   * Check if a file path represents a core file
   */
  private isCoreFile(filePath: string): boolean {
    const fileName = filePath.split(/[/\\]/).pop()?.toLowerCase() || "";
    return (
      fileName === "package.json" ||
      fileName.includes("main.") ||
      fileName.includes("index.") ||
      fileName.includes("app.") ||
      fileName.includes("agent-config")
    );
  }

  /**
   * Normalize file path relative to project root
   */
  private normalizePath(filePath: string, projectPath: string): string {
    return filePath.replace(projectPath, "").replace(/^[/\\]/, "");
  }

  /**
   * Discover additional configuration files dynamically
   */
  private async discoverAdditionalFiles(projectPath: string): Promise<FileAnalysisResult[]> {
    const additionalPatterns = [
      "vite.config.js", "vite.config.ts",
      "tsconfig.json", "tsconfig.app.json", "tsconfig.node.json",
      "eslint.config.js", "eslint.config.ts", ".eslintrc.js", ".eslintrc.json",
      "tailwind.config.js", "tailwind.config.ts",
      "postcss.config.js", "postcss.config.ts",
      ".env", ".env.local", ".env.development", ".env.production",
      "README.md", "CHANGELOG.md",
      "yarn.lock", "package-lock.json", "pnpm-lock.yaml",
    ];

    const results: FileAnalysisResult[] = [];

    for (const pattern of additionalPatterns) {
      const filePath = join(projectPath, pattern);

      if (existsSync(filePath)) {
        const content = await this.fsManager.readFile(filePath);
        const contextDescription = this.generateContextForFile(pattern);

        const analysisResult = await this.analyzeFileWithEnhancedLLM(filePath, content, contextDescription);

        results.push({
          filePath,
          content,
          exists: true,
          analysis: analysisResult.analysis,
          dependencies: analysisResult.dependencies,
          devDependencies: analysisResult.devDependencies,
          priority: this.getFilePriority(pattern),
          category: analysisResult.category,
        });
      }
    }

    return results;
  }

  /**
   * Generate context description for a file based on its name
   */
  private generateContextForFile(fileName: string): string {
    if (fileName.includes("vite.config")) return "Vite build configuration - analyze build settings, plugins, and optimizations for CSCS Agent compatibility";
    if (fileName.includes("tsconfig")) return "TypeScript configuration - examine compiler options, paths, and type checking settings";
    if (fileName.includes("eslint")) return "ESLint configuration - review linting rules and code quality settings";
    if (fileName.includes("tailwind")) return "Tailwind CSS configuration - analyze styling configuration and customizations";
    if (fileName.includes("postcss")) return "PostCSS configuration - examine CSS processing and plugin setup";
    if (fileName.includes(".env")) return "Environment variables - review configuration and security considerations";
    if (fileName.includes("README")) return "Project documentation - analyze project description and setup instructions";
    if (fileName.includes("lock")) return "Dependency lock file - examine locked dependency versions and integrity";

    return `Configuration file analysis - examine ${fileName} for CSCS Agent project compatibility and best practices`;
  }

  /**
   * Get priority for file analysis
   */
  private getFilePriority(fileName: string): number {
    if (fileName.includes("tsconfig")) return 7;
    if (fileName.includes("vite.config")) return 6;
    if (fileName.includes("eslint")) return 5;
    if (fileName.includes("tailwind")) return 4;
    if (fileName.includes(".env")) return 3;
    if (fileName.includes("README")) return 2;
    if (fileName.includes("lock")) return 1;

    return 0;
  }

  /**
   * Generate project summary using LLM analysis
   */
  private async generateProjectSummary(files: FileAnalysisResult[], projectPath: string): Promise<string> {
    if (!this.agent) {
      return "Project summary not available - OpenAI API key required for detailed analysis";
    }

    try {
      const filesSummary = files
        .filter(f => f.exists && f.analysis)
        .map(f => `${this.normalizePath(f.filePath, projectPath)}: ${f.category || 'unknown'} - ${f.analysis?.substring(0, 200)}...`)
        .join('\n');

      const prompt = `Based on the following file analysis, provide a comprehensive project summary:

${filesSummary}

Please provide:
1. Project type and architecture overview
2. Key technologies and frameworks used
3. CSCS Agent integration level and patterns
4. Overall project health and code quality
5. Upgrade readiness and potential challenges
6. Recommendations for modernization

Keep the summary concise but comprehensive.`;

      const result = await run(this.agent, prompt);
      return result.finalOutput || "Failed to generate project summary";
    } catch (error) {
      Logger.warning(`Failed to generate project summary: ${error instanceof Error ? error.message : "Unknown error"}`);
      return "Project summary generation failed - using basic analysis";
    }
  }

  /**
   * Analyze a file using enhanced LLM prompts with intelligent categorization
   */
  private async analyzeFileWithLLM(filePath: string, content: string, context: string): Promise<string> {
    // If no agent is available, provide basic analysis
    if (!this.agent) {
      return this.provideFallbackAnalysis(filePath, content, context);
    }

    try {
      const fileName = filePath.split(/[/\\]/).pop() || "unknown";
      const fileExtension = fileName.split(".").pop()?.toLowerCase() || "";

      const enhancedPrompt = `You are analyzing a file in a CSCS Agent project. Use your expertise to provide intelligent analysis.

CONTEXT: ${context}

FILE INFORMATION:
- Path: ${filePath}
- Name: ${fileName}
- Extension: ${fileExtension}

FILE CONTENT:
\`\`\`${fileExtension}
${content}
\`\`\`

ANALYSIS INSTRUCTIONS:
1. AUTOMATICALLY IDENTIFY the file type and its role in the project based on:
   - File path and name patterns
   - File extension and content structure
   - Import statements and dependencies
   - Code patterns and frameworks used

2. CATEGORIZE the file (e.g., package-config, entry-point, react-component, build-config, etc.)

3. ANALYZE the file's characteristics:
   - Purpose and functionality
   - Key dependencies and their versions
   - Configuration patterns and customizations
   - Code quality and modern practices
   - CSCS Agent framework integration level

4. IDENTIFY potential issues:
   - Outdated patterns or deprecated features
   - Security concerns or vulnerabilities
   - Performance optimization opportunities
   - Compatibility issues with latest standards

5. PROVIDE specific recommendations:
   - Upgrade paths and migration steps
   - Best practices alignment
   - Framework-specific improvements
   - Dependency updates needed

6. ASSESS upgrade complexity:
   - Rate the difficulty of upgrading this file (Low/Medium/High)
   - Highlight breaking changes or major refactoring needs
   - Suggest preservation of custom modifications

FORMAT your response as a structured analysis that can guide upgrade decisions.
Be specific about version compatibility and provide actionable insights.`;

      const result = await run(this.agent, enhancedPrompt);

      if (!result.finalOutput || typeof result.finalOutput !== "string") {
        return "LLM analysis failed - no valid response received";
      }

      return result.finalOutput;
    } catch (error) {
      Logger.warning(
        `LLM analysis failed for ${filePath}: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
      return this.provideFallbackAnalysis(filePath, content, context);
    }
  }

  /**
   * Provide basic fallback analysis when LLM is not available
   */
  private provideFallbackAnalysis(filePath: string, content: string, context: string): string {
    const fileName = filePath.split(/[/\\]/).pop() || "file";
    const lines = content.split("\n").length;
    const size = content.length;

    let analysis = `Basic analysis for ${fileName}:\n`;
    analysis += `- File size: ${size} characters, ${lines} lines\n`;
    analysis += `- Context: ${context}\n`;

    // Basic pattern detection
    if (fileName === "package.json") {
      try {
        const pkg = JSON.parse(content);
        analysis += `- Package name: ${pkg.name || "unknown"}\n`;
        analysis += `- Version: ${pkg.version || "unknown"}\n`;
        analysis += `- Dependencies: ${Object.keys(pkg.dependencies || {}).length}\n`;
        analysis += `- Dev dependencies: ${Object.keys(pkg.devDependencies || {}).length}\n`;
      } catch {
        analysis += "- Invalid JSON format\n";
      }
    } else if (fileName.endsWith(".tsx") || fileName.endsWith(".ts")) {
      const imports = content.match(/import .* from .*/g) || [];
      analysis += `- Import statements: ${imports.length}\n`;
      analysis += `- Contains React: ${content.includes("React") ? "yes" : "no"}\n`;
      analysis += `- Contains CSCS Agent: ${content.includes("@cscs-agent") ? "yes" : "no"}\n`;
    }

    analysis +=
      "\nNote: Limited analysis available without OpenAI API key. For detailed analysis, set OPENAI_API_KEY environment variable.";

    return analysis;
  }

  /**
   * Validate if this is a valid agent project
   */
  private validateAgentProject(
    packageJson: FileAnalysisResult,
    mainTsx: FileAnalysisResult,
    _agentConfig: FileAnalysisResult,
  ): boolean {
    // Check if package.json exists and has CSCS Agent dependencies
    if (!packageJson.exists || !packageJson.dependencies) {
      return false;
    }

    const hasCorePackage =
      Object.keys(packageJson.dependencies).some((dep) => dep.includes("@cscs-agent/core")) ||
      Object.keys(packageJson.devDependencies || {}).some((dep) => dep.includes("@cscs-agent/core"));

    // Check if main entry point exists
    if (!mainTsx.exists) {
      return false;
    }

    // Check if main.tsx imports from @cscs-agent/core
    const hasAgentImports = mainTsx.content.includes("@cscs-agent/core");

    // Agent config is optional but recommended
    return hasCorePackage && hasAgentImports;
  }
}
