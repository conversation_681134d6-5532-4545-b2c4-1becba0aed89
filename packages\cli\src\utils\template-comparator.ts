/**
 * Template comparator using LLM integration
 * Compares project files against the latest template to identify differences
 */

import { dirname, join } from "path";
import { fileURLToPath } from "url";

import { Agent, run } from "@openai/agents";

import type { FileDifference, PackageJson, ProjectAnalysisResult, TemplateComparisonResult } from "../types.js";
import { FileSystemManager } from "./filesystem-manager.js";
import { Logger } from "./logger.js";

export class TemplateComparator {
  private fsManager: FileSystemManager;
  private agent?: Agent;
  private templatePath: string;

  constructor() {
    this.fsManager = new FileSystemManager();

    // Get template path
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = dirname(__filename);
    this.templatePath = join(__dirname, "..", "..", "templates", "basic");

    // Initialize the LLM agent for template comparison only if API key is available
    if (process.env.OPENAI_API_KEY) {
      this.agent = new Agent({
        name: "Template<PERSON>omparator",
        instructions: `You are an expert TypeScript/React developer specializing in CSCS Agent projects.
          Your task is to compare project files against the latest template and identify differences.

          When comparing files, identify:
          1. Dependency version differences
          2. Configuration changes
          3. Structural differences
          4. Missing or outdated patterns
          5. Custom modifications that should be preserved

          For each difference, specify:
          - Type: dependency, configuration, code, or structure
          - Description: clear explanation of the difference
          - Template value: what the template has
          - Project value: what the project currently has
          - Action: add, update, remove, or merge
          - Priority: critical, recommended, or optional

          Return your analysis as a structured JSON object with the differences array.`,
        model: process.env.OPENAI_MODEL || "gpt-4o",
      });
    }
  }

  /**
   * Compare project against template
   */
  async compareWithTemplate(projectAnalysis: ProjectAnalysisResult): Promise<TemplateComparisonResult[]> {
    Logger.info("Comparing project with latest template...");

    const comparisons: TemplateComparisonResult[] = [];

    // Compare package.json
    if (projectAnalysis.packageJson.exists) {
      const packageComparison = await this.comparePackageJson(projectAnalysis.packageJson);
      if (packageComparison) {
        comparisons.push(packageComparison);
      }
    }

    // Compare main.tsx
    if (projectAnalysis.mainTsx.exists) {
      const mainComparison = await this.compareMainTsx(projectAnalysis.mainTsx);
      if (mainComparison) {
        comparisons.push(mainComparison);
      }
    }

    // Compare agent-config
    if (projectAnalysis.agentConfig.exists) {
      const configComparison = await this.compareAgentConfig(projectAnalysis.agentConfig);
      if (configComparison) {
        comparisons.push(configComparison);
      }
    }

    // Compare additional configuration files
    for (const file of projectAnalysis.additionalFiles) {
      const comparison = await this.compareConfigFile(file);
      if (comparison) {
        comparisons.push(comparison);
      }
    }

    return comparisons;
  }

  /**
   * Compare package.json with template
   */
  private async comparePackageJson(projectFile: any): Promise<TemplateComparisonResult | null> {
    const templatePackagePath = join(this.templatePath, "package.json");

    if (!this.fsManager.fileExists(templatePackagePath)) {
      Logger.warning("Template package.json not found");
      return null;
    }

    const templateContent = await this.fsManager.readFile(templatePackagePath);

    try {
      const templatePackage: PackageJson = JSON.parse(templateContent);
      const projectPackage: PackageJson = JSON.parse(projectFile.content);

      const differences = await this.comparePackageJsonWithLLM(
        projectPackage,
        templatePackage,
        projectFile.content,
        templateContent,
      );

      return {
        file: "package.json",
        templateContent,
        projectContent: projectFile.content,
        differences,
        requiresUpdate: differences.length > 0,
        updatePriority: this.determineUpdatePriority(differences),
      };
    } catch (error) {
      Logger.warning(`Failed to compare package.json: ${error instanceof Error ? error.message : "Unknown error"}`);
      return null;
    }
  }

  /**
   * Compare main.tsx with template
   */
  private async compareMainTsx(projectFile: any): Promise<TemplateComparisonResult | null> {
    const templateMainPath = join(this.templatePath, "src", "main.tsx");

    if (!this.fsManager.fileExists(templateMainPath)) {
      Logger.warning("Template main.tsx not found");
      return null;
    }

    const templateContent = await this.fsManager.readFile(templateMainPath);

    const differences = await this.compareFileWithLLM(
      projectFile.content,
      templateContent,
      "main.tsx",
      "This is the main application entry point. Compare imports, router configuration, and app initialization.",
    );

    return {
      file: "src/main.tsx",
      templateContent,
      projectContent: projectFile.content,
      differences,
      requiresUpdate: differences.length > 0,
      updatePriority: this.determineUpdatePriority(differences),
    };
  }

  /**
   * Compare agent-config with template
   */
  private async compareAgentConfig(projectFile: any): Promise<TemplateComparisonResult | null> {
    const templateConfigPath = join(this.templatePath, "src", "agent-config.tsx");

    if (!this.fsManager.fileExists(templateConfigPath)) {
      Logger.warning("Template agent-config.tsx not found");
      return null;
    }

    const templateContent = await this.fsManager.readFile(templateConfigPath);

    const differences = await this.compareFileWithLLM(
      projectFile.content,
      templateContent,
      "agent-config.tsx",
      "This is the agent configuration file. Compare agent definitions, structure, and patterns while preserving custom configurations.",
    );

    return {
      file: "src/agent-config.tsx",
      templateContent,
      projectContent: projectFile.content,
      differences,
      requiresUpdate: differences.length > 0,
      updatePriority: this.determineUpdatePriority(differences),
    };
  }

  /**
   * Compare configuration file with template
   */
  private async compareConfigFile(projectFile: any): Promise<TemplateComparisonResult | null> {
    const fileName = projectFile.filePath.split(/[/\\]/).pop();
    const templateFilePath = join(this.templatePath, fileName);

    if (!this.fsManager.fileExists(templateFilePath)) {
      // File doesn't exist in template, might be custom
      return null;
    }

    const templateContent = await this.fsManager.readFile(templateFilePath);

    const differences = await this.compareFileWithLLM(
      projectFile.content,
      templateContent,
      fileName,
      `This is a ${fileName} configuration file. Compare configurations and identify outdated patterns.`,
    );

    return {
      file: fileName,
      templateContent,
      projectContent: projectFile.content,
      differences,
      requiresUpdate: differences.length > 0,
      updatePriority: this.determineUpdatePriority(differences),
    };
  }

  /**
   * Compare package.json files using LLM
   */
  private async comparePackageJsonWithLLM(
    projectPackage: PackageJson,
    templatePackage: PackageJson,
    projectContent: string,
    templateContent: string,
  ): Promise<FileDifference[]> {
    const prompt = `Compare these two package.json files and identify differences:

PROJECT package.json:
\`\`\`json
${projectContent}
\`\`\`

TEMPLATE package.json:
\`\`\`json
${templateContent}
\`\`\`

Focus on:
1. Dependency version differences
2. Missing dependencies in the project
3. Outdated dependencies in the project
4. Script differences
5. Configuration differences

Return a JSON array of differences with this structure:
[
  {
    "type": "dependency|configuration|code|structure",
    "description": "Clear description of the difference",
    "templateValue": "value from template",
    "projectValue": "value from project",
    "action": "add|update|remove|merge"
  }
]`;

    return await this.getLLMDifferences(prompt);
  }

  /**
   * Compare files using LLM
   */
  private async compareFileWithLLM(
    projectContent: string,
    templateContent: string,
    fileName: string,
    context: string,
  ): Promise<FileDifference[]> {
    const prompt = `${context}

Compare these two ${fileName} files:

PROJECT ${fileName}:
\`\`\`
${projectContent}
\`\`\`

TEMPLATE ${fileName}:
\`\`\`
${templateContent}
\`\`\`

Identify differences and return a JSON array with this structure:
[
  {
    "type": "dependency|configuration|code|structure",
    "description": "Clear description of the difference",
    "templateValue": "value from template",
    "projectValue": "value from project",
    "action": "add|update|remove|merge"
  }
]`;

    return await this.getLLMDifferences(prompt);
  }

  /**
   * Get differences from LLM response or provide basic comparison
   */
  private async getLLMDifferences(prompt: string): Promise<FileDifference[]> {
    // If no agent is available, return empty array (basic comparison will be used)
    if (!this.agent) {
      Logger.warning("LLM comparison not available without OpenAI API key. Using basic comparison.");
      return [];
    }

    try {
      const result = await run(this.agent, prompt);

      if (!result.finalOutput || typeof result.finalOutput !== "string") {
        return [];
      }

      // Try to extract JSON from the response
      const jsonMatch = result.finalOutput.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        Logger.warning("No valid JSON found in LLM response");
        return [];
      }

      const differences: FileDifference[] = JSON.parse(jsonMatch[0]);
      return differences;
    } catch (error) {
      Logger.warning(`Failed to get LLM differences: ${error instanceof Error ? error.message : "Unknown error"}`);
      return [];
    }
  }

  /**
   * Determine update priority based on differences
   */
  private determineUpdatePriority(differences: FileDifference[]): "critical" | "recommended" | "optional" {
    const hasCritical = differences.some(
      (diff) =>
        (diff.type === "dependency" && diff.action === "update") ||
        diff.type === "structure" ||
        diff.description.toLowerCase().includes("security") ||
        diff.description.toLowerCase().includes("breaking"),
    );

    if (hasCritical) {
      return "critical";
    }

    const hasRecommended = differences.some((diff) => diff.type === "dependency" || diff.type === "configuration");

    return hasRecommended ? "recommended" : "optional";
  }
}
